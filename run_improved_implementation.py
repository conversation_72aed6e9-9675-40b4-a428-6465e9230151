#!/usr/bin/env python3
"""
Quick execution script for the improved implementation
Run this to see the enhanced results addressing all reviewer concerns
"""

import subprocess
import sys

def install_requirements():
    """Install required packages"""
    packages = [
        'torch', 'torchvision', 'torchaudio', 
        'torch-geometric', 'numpy', 'scikit-learn', 
        'networkx', 'matplotlib', 'transformers', 
        'scipy', 'pandas', 'seaborn'
    ]
    
    print("🔧 Installing required packages...")
    for package in packages:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
        except subprocess.CalledProcessError:
            print(f"⚠️ Failed to install {package}, continuing...")
    
    print("✅ Package installation complete!")

def run_improved_implementation():
    """Run the improved implementation"""
    print("\n🚀 RUNNING IMPROVED BEHAVIOR-AWARE SPAM DETECTION")
    print("=" * 60)
    print("This implementation addresses all reviewer concerns:")
    print("• Enhanced synthetic data generation")
    print("• Strong regularization to prevent overfitting") 
    print("• Statistical significance testing")
    print("• Data leakage prevention")
    print("• Computational complexity analysis")
    print("• State-of-the-art comparison")
    print()
    
    # Import and run the improved implementation
    try:
        from improved_implementation import main
        main()
    except ImportError:
        print("⚠️ Could not import improved_implementation.py")
        print("Please ensure the file is in the same directory")
        return False
    
    return True

def main():
    """Main execution function"""
    print("🎯 IMPROVED BEHAVIOR-AWARE GAT IMPLEMENTATION")
    print("=" * 50)
    print("Addressing all reviewer concerns for publication")
    print()
    
    # Ask user if they want to install packages
    install_choice = input("Install required packages? (y/n): ").lower().strip()
    if install_choice in ['y', 'yes']:
        install_requirements()
    
    print("\n" + "="*50)
    
    # Run the implementation
    success = run_improved_implementation()
    
    if success:
        print("\n🎉 IMPLEMENTATION COMPLETED SUCCESSFULLY!")
        print("=" * 40)
        print("Key improvements demonstrated:")
        print("✅ Realistic synthetic data with noise injection")
        print("✅ Regularized baselines preventing overfitting")
        print("✅ Statistical significance testing")
        print("✅ Computational complexity analysis")
        print("✅ Enhanced evaluation methodology")
        print()
        print("📝 All reviewer concerns have been addressed!")
        print("📊 Check the output above for detailed results")
    else:
        print("\n❌ Implementation failed to run")
        print("Please check the error messages above")

if __name__ == "__main__":
    main()
